{"name": "fast-ocr", "productName": "fast-ocr", "version": "1.0.0", "description": "My Electron application description", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "npm run build && electron-forge start", "dev": "npm run build && electron-forge start", "package": "npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": {"name": "jf", "email": "https://gitlab.hunantianxing.com/"}, "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1"}, "devDependencies": {"@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/plugin-auto-unpack-natives": "^7.8.2", "@electron-forge/plugin-fuses": "^7.8.2", "@electron/fuses": "^1.8.0", "@types/node": "^24.1.0", "electron": "37.2.4", "typescript": "^5.8.3"}}